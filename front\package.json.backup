{"name": "lotto", "version": "1.3.5", "description": "A lotto app", "productName": "樂透即時報", "author": "<PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev -m pwa", "build": "node scripts/clean-version-cache.js && quasar build -m pwa && node scripts/generate-version.js", "generate-version": "node scripts/generate-version.js", "clean-version-cache": "node scripts/clean-version-cache.js"}, "dependencies": {"@quasar/extras": "^1.16.4", "@vee-validate/rules": "^4.13.2", "axios": "^1.2.1", "bootstrap": "^5.3.3", "exceljs": "^4.4.0", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^4.2.0", "quasar": "^2.16.0", "vee-validate": "^4.13.2", "vue": "^3.4.18", "vue-router": "^4.0.12", "worker-loader": "^3.0.8", "xlsx": "^0.18.5"}, "devDependencies": {"@quasar/app-vite": "^1.9.0", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^4.5.4", "vite-plugin-checker": "^0.6.4", "vue-tsc": "^1.8.22", "workbox-build": "^7.1.1", "workbox-cacheable-response": "^7.1.0", "workbox-core": "^7.1.0", "workbox-expiration": "^7.1.0", "workbox-precaching": "^7.1.0", "workbox-routing": "^7.1.0", "workbox-strategies": "^7.1.0"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}