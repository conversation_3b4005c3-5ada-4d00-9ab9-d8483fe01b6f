@echo off
setlocal enabledelayedexpansion

echo 建置所有批次分析工具...

@REM 將命令提示符的代碼頁更改為 UTF-8，以支持顯示中文字符。
chcp 65001 > nul

@REM 建置調度器
echo 建置調度器...
go build -o batch_analyzer.exe ./cmd/scheduler/main.go
if %errorlevel% neq 0 (
    echo 調度器建置失敗！
    exit /b %errorlevel%
)
echo 調度器建置成功：batch_analyzer.exe

@REM 建置測試工具
echo 建置測試工具...
go build -o test_analyzer.exe ./cmd/test/main.go
if %errorlevel% neq 0 (
    echo 測試工具建置失敗！
    exit /b %errorlevel%
)
echo 測試工具建置成功：test_analyzer.exe

@REM 建置配置初始化工具
echo 建置配置初始化工具...
go build -o init_config.exe ./cmd/init/main.go
if %errorlevel% neq 0 (
    echo 配置初始化工具建置失敗！
    exit /b %errorlevel%
)
echo 配置初始化工具建置成功：init_config.exe

echo.
echo 所有工具建置完成！
echo - batch_analyzer.exe    : 批次分析調度器
echo - test_analyzer.exe     : 測試工具
echo - init_config.exe       : 配置初始化工具
