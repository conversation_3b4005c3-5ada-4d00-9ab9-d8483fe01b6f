# 批次分析調度器

這是一個自動化的彩票批次分析系統，可以在每天晚上 10:30 自動執行版路分析，並將結果儲存到資料庫中。

## 功能特點

- **自動調度**: 每晚 22:30 自動執行分析
- **啟動分析**: 調度器啟動時自動檢查並執行一次分析
- **重複檢查**: 智能避免重複計算，節省資源
- **版路分析**: 實現與前端 analyzer.worker.ts 相同的核心計算邏輯
- **資料庫儲存**: 完整記錄分析參數、結果、預測號碼和命中情況
- **API 接口**: 提供完整的 REST API 來查詢分析結果
- **彈性架構**: 支援多種彩種，易於擴展

## 系統架構

### 資料庫表結構

1. **batch_analysis_configs** - 分析參數配置
2. **batch_analysis_jobs** - 分析任務記錄
3. **batch_analysis_results** - 分析結果
4. **batch_prediction_numbers** - 預測號碼記錄
5. **batch_prediction_hits** - 預測命中記錄

### 核心組件

1. **Analyzer** (`analyzer.go`) - 核心分析引擎
2. **Service** (`service.go`) - 批次分析服務
3. **Scheduler** (`scheduler.go`) - 定時調度器
4. **Controller** (`../controllers/batch_analysis.go`) - API 控制器

## 安裝和配置

### 1. 資料庫遷移

```bash
cd api
go run migrate.bat
```

### 2. 初始化配置

```bash
cd api/batch-analyzer
go run ./cmd/init/main.go
```

### 3. 建置工具

```bash
# 建置所有工具
./build_all.bat

# 或者單獨建置調度器
./build.bat
```

### 4. 啟動調度器

```bash
# Windows
batch_analyzer.exe

# Linux/Mac
./batch_analyzer
```

## 配置參數

根據 `v1.3.5_todo.md` 的要求，系統會自動創建以下配置：

- **分析方法**: 版路分析 (ball-follow)
- **彩種**: 今彩539 (daily539)
- **拖牌組合**: 全部 27 種組合 (1,1,1 到 3,3,3)
- **推算期數**: 30-300期，間隔30期 (10個值)
- **拖牌區間**: 10-30期，間隔5期 (5個值)
- **預測期數**: 下1期
- **分析期數**: 50期
- **連續拖出次數**: 3次(含)以上
- **準確率**: 100%(含)以上
- **統計方式**: 預測組數統計

## API 接口

### 配置管理

- `GET /api/batch-analysis/configs` - 獲取配置列表
- `GET /api/batch-analysis/configs/:id` - 獲取單個配置
- `POST /api/batch-analysis/configs` - 創建配置
- `PUT /api/batch-analysis/configs/:id` - 更新配置
- `DELETE /api/batch-analysis/configs/:id` - 刪除配置

### 任務管理

- `GET /api/batch-analysis/jobs` - 獲取任務列表
- `GET /api/batch-analysis/jobs/:id` - 獲取單個任務
- `POST /api/batch-analysis/trigger` - 手動觸發分析
- `POST /api/batch-analysis/trigger?force=true` - 強制執行分析（忽略重複檢查）

### 結果查詢

- `GET /api/batch-analysis/results` - 獲取分析結果
- `GET /api/batch-analysis/predictions` - 獲取預測號碼
- `GET /api/batch-analysis/hits` - 獲取命中記錄
- `GET /api/batch-analysis/statistics` - 獲取統計信息

### 查詢參數

所有列表接口都支援以下參數：

- `page` - 頁碼 (預設: 1)
- `page_size` - 每頁數量 (預設: 20)
- `lottery_type` - 彩種篩選
- `config_id` - 配置ID篩選
- `job_id` - 任務ID篩選

## 測試

```bash
cd api/batch-analyzer
go run ./cmd/test/main.go
# 或者使用建置後的執行檔
test_analyzer.exe
```

## 日誌

調度器會記錄詳細的執行日誌，包括：

- 任務啟動和完成時間
- 分析進度
- 錯誤信息
- 性能統計

## 注意事項

1. **記憶體使用**: 大量組合計算可能消耗較多記憶體
2. **執行時間**: 完整分析可能需要較長時間
3. **資料依賴**: 需要足夠的歷史開獎數據
4. **並發控制**: 同時只能執行一個分析任務
5. **重複檢查**: 系統會自動避免重複計算，如需強制執行請使用 `force=true` 參數
6. **啟動分析**: 調度器啟動時會自動檢查並執行一次分析（如果需要）

## 擴展功能

系統設計為可擴展架構，未來可以：

1. 支援其他分析方法 (尾數分析、綜合分析)
2. 支援其他彩種
3. 添加更多篩選條件
4. 實現分散式計算
5. 添加實時通知功能

## 故障排除

### 常見問題

1. **資料庫連接失敗**: 檢查 .env 配置
2. **分析失敗**: 檢查歷史數據是否充足
3. **記憶體不足**: 調整配置參數或增加系統記憶體
4. **調度器無法啟動**: 檢查端口占用和權限

### 日誌位置

- 調度器日誌: 控制台輸出
- API 日誌: `api/log/` 目錄
- 錯誤日誌: 資料庫 `error_message` 欄位
