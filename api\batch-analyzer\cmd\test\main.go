package main

import (
	"fmt"
	"log"
	batch_analyzer "lottery/batch-analyzer"
	"lottery/database"
	"lottery/models"

	"gorm.io/gorm"
)

func main() {
	log.Println("開始測試批次分析系統...")
	
	// 連接資料庫
	db := database.ConnectDB()
	
	// 測試資料庫連接
	if err := testDatabaseConnection(db); err != nil {
		log.Fatalf("資料庫連接測試失敗: %v", err)
	}
	
	// 測試分析器
	if err := testAnalyzer(); err != nil {
		log.Fatalf("分析器測試失敗: %v", err)
	}
	
	// 測試服務
	if err := testService(); err != nil {
		log.Fatalf("服務測試失敗: %v", err)
	}
	
	log.Println("所有測試通過！")
}

func testDatabaseConnection(db *gorm.DB) error {
	log.Println("測試資料庫連接...")
	
	// 測試基本查詢
	var count int64
	if err := db.Model(&models.BatchAnalysisConfig{}).Count(&count).Error; err != nil {
		return fmt.Errorf("查詢配置表失敗: %v", err)
	}
	
	log.Printf("配置表記錄數: %d", count)
	return nil
}

func testAnalyzer() error {
	log.Println("測試分析器...")
	
	// 創建測試數據
	testResults := []batch_analyzer.DrawResult{
		{Numbers: []int{1, 2, 3, 4, 5}, Period: "1"},
		{Numbers: []int{2, 3, 4, 5, 6}, Period: "2"},
		{Numbers: []int{3, 4, 5, 6, 7}, Period: "3"},
		{Numbers: []int{4, 5, 6, 7, 8}, Period: "4"},
		{Numbers: []int{5, 6, 7, 8, 9}, Period: "5"},
		{Numbers: []int{6, 7, 8, 9, 10}, Period: "6"},
		{Numbers: []int{7, 8, 9, 10, 11}, Period: "7"},
		{Numbers: []int{8, 9, 10, 11, 12}, Period: "8"},
		{Numbers: []int{9, 10, 11, 12, 13}, Period: "9"},
		{Numbers: []int{10, 11, 12, 13, 14}, Period: "10"},
	}
	
	// 創建分析配置
	config := batch_analyzer.AnalysisConfig{
		FirstGroupSize:  1,
		SecondGroupSize: 1,
		TargetGroupSize: 1,
		MaxRange:        3,
		LookAheadCount:  1,
	}
	
	// 創建分析器
	analyzer := batch_analyzer.NewLottoAnalyzer(config, testResults)
	
	// 執行分析
	results, occurrences, err := analyzer.AnalyzeWithProgress()
	if err != nil {
		return fmt.Errorf("分析執行失敗: %v", err)
	}
	
	log.Printf("分析結果數量: %d", len(results))
	log.Printf("出現統計數量: %d", len(occurrences))
	
	// 顯示前幾個結果
	for i, result := range results {
		if i >= 3 {
			break
		}
		log.Printf("結果 %d: 第一組=%v, 第二組=%v, 目標組=%v, 準確率=%.4f, 連續拖出=%d",
			i+1, result.FirstNumbers, result.SecondNumbers, result.TargetNumbers,
			result.TargetProbability, result.ConsecutiveHits)
	}
	
	return nil
}

func testService() error {
	log.Println("測試批次分析服務...")
	
	// 創建服務
	service := batch_analyzer.NewBatchAnalysisService()
	_ = service // 使用變數避免編譯警告
	
	log.Println("服務測試完成")
	return nil
}
