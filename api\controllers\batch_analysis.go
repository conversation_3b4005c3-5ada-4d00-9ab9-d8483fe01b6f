package controllers

import (
	"database/sql"
	batch_analyzer "lottery/batch-analyzer"
	"lottery/database"
	"lottery/models"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BatchAnalysisController 批次分析控制器
type BatchAnalysisController struct {
	db        *gorm.DB
	scheduler *batch_analyzer.Scheduler
}

// NewBatchAnalysisController 創建新的批次分析控制器
func NewBatchAnalysisController() *BatchAnalysisController {
	return &BatchAnalysisController{
		db:        database.ConnectDB(),
		scheduler: batch_analyzer.NewScheduler(),
	}
}

// GetConfigs 獲取分析配置列表
func (bac *BatchAnalysisController) GetConfigs(c *gin.Context) {
	var configs []models.BatchAnalysisConfig

	// 分頁參數
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>uer<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	offset := (page - 1) * pageSize

	// 篩選參數
	lotteryType := c.Query("lottery_type")
	isActive := c.Query("is_active")

	query := bac.db.Model(&models.BatchAnalysisConfig{})

	if lotteryType != "" {
		query = query.Where("lottery_type = ?", lotteryType)
	}

	if isActive != "" {
		query = query.Where("is_active = ?", isActive == "true")
	}

	// 獲取總數
	var total int64
	query.Count(&total)

	// 獲取數據
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&configs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "獲取配置列表失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": configs,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetConfig 獲取單個配置
func (bac *BatchAnalysisController) GetConfig(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "無效的配置ID",
		})
		return
	}

	var config models.BatchAnalysisConfig
	if err := bac.db.First(&config, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "配置不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "獲取配置失敗",
				"msg":   err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": config,
	})
}

// CreateConfig 創建配置
func (bac *BatchAnalysisController) CreateConfig(c *gin.Context) {
	var config models.BatchAnalysisConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "請求參數錯誤",
			"msg":   err.Error(),
		})
		return
	}

	if err := bac.db.Create(&config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "創建配置失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"data": config,
		"msg":  "配置創建成功",
	})
}

// UpdateConfig 更新配置
func (bac *BatchAnalysisController) UpdateConfig(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "無效的配置ID",
		})
		return
	}

	var config models.BatchAnalysisConfig
	if err := bac.db.First(&config, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "配置不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "獲取配置失敗",
				"msg":   err.Error(),
			})
		}
		return
	}

	var updateData models.BatchAnalysisConfig
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "請求參數錯誤",
			"msg":   err.Error(),
		})
		return
	}

	if err := bac.db.Model(&config).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新配置失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": config,
		"msg":  "配置更新成功",
	})
}

// DeleteConfig 刪除配置
func (bac *BatchAnalysisController) DeleteConfig(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "無效的配置ID",
		})
		return
	}

	if err := bac.db.Delete(&models.BatchAnalysisConfig{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "刪除配置失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "配置刪除成功",
	})
}

// GetJobs 獲取任務列表
func (bac *BatchAnalysisController) GetJobs(c *gin.Context) {
	var jobs []models.BatchAnalysisJob

	// 分頁參數
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	offset := (page - 1) * pageSize

	// 篩選參數
	configID := c.Query("config_id")
	lotteryType := c.Query("lottery_type")
	status := c.Query("status")

	query := bac.db.Model(&models.BatchAnalysisJob{}).Preload("Config")

	if configID != "" {
		query = query.Where("config_id = ?", configID)
	}

	if lotteryType != "" {
		query = query.Where("lottery_type = ?", lotteryType)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 獲取總數
	var total int64
	query.Count(&total)

	// 獲取數據
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&jobs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "獲取任務列表失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": jobs,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetJob 獲取單個任務
func (bac *BatchAnalysisController) GetJob(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "無效的任務ID",
		})
		return
	}

	var job models.BatchAnalysisJob
	if err := bac.db.Preload("Config").First(&job, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "任務不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "獲取任務失敗",
				"msg":   err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": job,
	})
}

// TriggerAnalysis 手動觸發分析
func (bac *BatchAnalysisController) TriggerAnalysis(c *gin.Context) {
	// 檢查是否強制執行
	force := c.Query("force") == "true"

	go func() {
		if err := bac.scheduler.TriggerManualAnalysisWithForce(force); err != nil {
			// 記錄錯誤，但不影響響應
			// log.Printf("手動觸發分析失敗: %v", err)
		}
	}()

	message := "分析任務已觸發，正在後台執行"
	if force {
		message = "強制分析任務已觸發，正在後台執行（忽略重複檢查）"
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": message,
	})
}

// GetResults 獲取分析結果
func (bac *BatchAnalysisController) GetResults(c *gin.Context) {
	var results []models.BatchAnalysisResult

	// 分頁參數
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	offset := (page - 1) * pageSize

	// 篩選參數
	jobID := c.Query("job_id")
	configID := c.Query("config_id")
	lotteryType := c.Query("lottery_type")
	analysisPeriod := c.Query("analysis_period")

	query := bac.db.Model(&models.BatchAnalysisResult{}).Preload("Job").Preload("Config")

	if jobID != "" {
		query = query.Where("job_id = ?", jobID)
	}

	if configID != "" {
		query = query.Where("config_id = ?", configID)
	}

	if lotteryType != "" {
		query = query.Where("lottery_type = ?", lotteryType)
	}

	if analysisPeriod != "" {
		query = query.Where("analysis_period = ?", analysisPeriod)
	}

	// 獲取總數
	var total int64
	query.Count(&total)

	// 獲取數據
	if err := query.Offset(offset).Limit(pageSize).Order("target_probability DESC, consecutive_hits DESC").Find(&results).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "獲取分析結果失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": results,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetPredictions 獲取預測號碼
func (bac *BatchAnalysisController) GetPredictions(c *gin.Context) {
	var predictions []models.BatchPredictionNumber

	// 分頁參數
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	offset := (page - 1) * pageSize

	// 篩選參數
	jobID := c.Query("job_id")
	configID := c.Query("config_id")
	lotteryType := c.Query("lottery_type")
	predictionType := c.Query("prediction_type")
	analysisPeriod := c.Query("analysis_period")

	query := bac.db.Model(&models.BatchPredictionNumber{}).Preload("Job").Preload("Config")

	if jobID != "" {
		query = query.Where("job_id = ?", jobID)
	}

	if configID != "" {
		query = query.Where("config_id = ?", configID)
	}

	if lotteryType != "" {
		query = query.Where("lottery_type = ?", lotteryType)
	}

	if predictionType != "" {
		query = query.Where("prediction_type = ?", predictionType)
	}

	if analysisPeriod != "" {
		query = query.Where("analysis_period = ?", analysisPeriod)
	}

	// 獲取總數
	var total int64
	query.Count(&total)

	// 獲取數據
	if err := query.Offset(offset).Limit(pageSize).Order("analysis_period DESC, sort_order ASC").Find(&predictions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "獲取預測號碼失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": predictions,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetHits 獲取命中記錄
func (bac *BatchAnalysisController) GetHits(c *gin.Context) {
	var hits []models.BatchPredictionHit

	// 分頁參數
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	offset := (page - 1) * pageSize

	// 篩選參數
	jobID := c.Query("job_id")
	configID := c.Query("config_id")
	lotteryType := c.Query("lottery_type")
	predictionID := c.Query("prediction_id")

	query := bac.db.Model(&models.BatchPredictionHit{}).Preload("Job").Preload("Config").Preload("Prediction")

	if jobID != "" {
		query = query.Where("job_id = ?", jobID)
	}

	if configID != "" {
		query = query.Where("config_id = ?", configID)
	}

	if lotteryType != "" {
		query = query.Where("lottery_type = ?", lotteryType)
	}

	if predictionID != "" {
		query = query.Where("prediction_id = ?", predictionID)
	}

	// 獲取總數
	var total int64
	query.Count(&total)

	// 獲取數據
	if err := query.Offset(offset).Limit(pageSize).Order("hit_rate DESC, hit_count DESC").Find(&hits).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "獲取命中記錄失敗",
			"msg":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": hits,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetStatistics 獲取統計信息
func (bac *BatchAnalysisController) GetStatistics(c *gin.Context) {
	lotteryType := c.Query("lottery_type")

	// 統計配置數量
	var configStats struct {
		Total  int64 `json:"total"`
		Active int64 `json:"active"`
	}

	bac.db.Model(&models.BatchAnalysisConfig{}).Count(&configStats.Total)
	query := bac.db.Model(&models.BatchAnalysisConfig{}).Where("is_active = ?", true)
	if lotteryType != "" {
		query = query.Where("lottery_type = ?", lotteryType)
	}
	query.Count(&configStats.Active)

	// 統計任務數量
	var jobStats struct {
		Total     int64 `json:"total"`
		Pending   int64 `json:"pending"`
		Running   int64 `json:"running"`
		Completed int64 `json:"completed"`
		Failed    int64 `json:"failed"`
	}

	jobQuery := bac.db.Model(&models.BatchAnalysisJob{})
	if lotteryType != "" {
		jobQuery = jobQuery.Where("lottery_type = ?", lotteryType)
	}

	jobQuery.Count(&jobStats.Total)
	jobQuery.Where("status = ?", "pending").Count(&jobStats.Pending)
	jobQuery.Where("status = ?", "running").Count(&jobStats.Running)
	jobQuery.Where("status = ?", "completed").Count(&jobStats.Completed)
	jobQuery.Where("status = ?", "failed").Count(&jobStats.Failed)

	// 統計預測命中率
	var hitStats struct {
		TotalPredictions int64   `json:"total_predictions"`
		TotalHits        int64   `json:"total_hits"`
		AverageHitRate   float64 `json:"average_hit_rate"`
	}

	hitQuery := bac.db.Model(&models.BatchPredictionHit{})
	if lotteryType != "" {
		hitQuery = hitQuery.Where("lottery_type = ?", lotteryType)
	}

	hitQuery.Count(&hitStats.TotalPredictions)
	hitQuery.Where("hit_count > 0").Count(&hitStats.TotalHits)

	var avgHitRate sql.NullFloat64
	hitQuery.Select("AVG(hit_rate)").Scan(&avgHitRate)
	if avgHitRate.Valid {
		hitStats.AverageHitRate = avgHitRate.Float64
	}

	c.JSON(http.StatusOK, gin.H{
		"config_stats": configStats,
		"job_stats":    jobStats,
		"hit_stats":    hitStats,
	})
}
