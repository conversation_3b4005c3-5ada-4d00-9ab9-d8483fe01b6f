package main

import (
	"os"
	"time"
	_ "time/tzdata"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	"lottery/database"
	"lottery/routes"
	. "lottery/utils"
)

func init() {
	log.SetFormatter(&log.JSONFormatter{})
	log.SetOutput(os.Stdout)

	// set time zone
	twTimeZone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		ErrorLog(ErrorMsg{
			Msg:   "無法設定時區",
			Error: err.Error(),
		})
	}

	time.Local = twTimeZone
}

func main() {
	// 連接資料庫
	db := database.ConnectDB()
	defer database.CloseDB(db)

	router := routes.Routes(db)

	go func() {
		if err := router.Run(":" + viper.GetString("APP_PORT")); err != nil {
			ErrorLog(ErrorMsg{
				Msg:   "無法啟動伺服器",
				Error: err.Error(),
			})
		}
	}()

	select {}
}
