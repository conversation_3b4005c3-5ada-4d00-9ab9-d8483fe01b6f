package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// NumberArray 自定義 JSON 數組類型
type NumberArray []int

func (na NumberArray) Value() (driver.Value, error) {
	return json.Marshal(na)
}

func (na *NumberArray) Scan(value interface{}) error {
	if value == nil {
		*na = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into NumberArray", value)
	}

	return json.Unmarshal(bytes, na)
}
