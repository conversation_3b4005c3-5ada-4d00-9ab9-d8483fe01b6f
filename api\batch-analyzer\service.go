package batch_analyzer

import (
	"fmt"
	"log"
	"lottery/database"
	"lottery/models"
	"sort"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// BatchAnalysisService 批次分析服務
type BatchAnalysisService struct {
	db *gorm.DB
}

// NewBatchAnalysisService 創建新的批次分析服務
func NewBatchAnalysisService() *BatchAnalysisService {
	return &BatchAnalysisService{
		db: database.ConnectDB(),
	}
}

// RunDailyAnalysis 執行每日分析
func (bas *BatchAnalysisService) RunDailyAnalysis() error {
	log.Println("開始執行每日批次分析...")

	// 獲取所有啟用的配置
	var configs []models.BatchAnalysisConfig
	if err := bas.db.Where("is_active = ?", true).Find(&configs).Error; err != nil {
		return fmt.Errorf("獲取分析配置失敗: %v", err)
	}

	if len(configs) == 0 {
		log.Println("沒有啟用的分析配置")
		return nil
	}

	// 對每個配置執行分析
	for _, config := range configs {
		if err := bas.runAnalysisForConfig(config); err != nil {
			log.Printf("配置 %s 分析失敗: %v", config.Name, err)
			continue
		}
		log.Printf("配置 %s 分析完成", config.Name)
	}

	log.Println("每日批次分析完成")
	return nil
}

// runAnalysisForConfig 為特定配置執行分析
func (bas *BatchAnalysisService) runAnalysisForConfig(config models.BatchAnalysisConfig) error {
	// 獲取最新的開獎日期作為參考日期
	referenceDate, err := bas.getLatestDrawDate(config.LotteryType)
	if err != nil {
		return fmt.Errorf("獲取最新開獎日期失敗: %v", err)
	}

	// 創建分析任務
	job := models.BatchAnalysisJob{
		ConfigID:      config.ID,
		LotteryType:   config.LotteryType,
		ReferenceDate: referenceDate,
		Status:        "pending",
	}

	if err := bas.db.Create(&job).Error; err != nil {
		return fmt.Errorf("創建分析任務失敗: %v", err)
	}

	// 執行分析
	return bas.executeAnalysisJob(&job, &config)
}

// executeAnalysisJob 執行分析任務
func (bas *BatchAnalysisService) executeAnalysisJob(job *models.BatchAnalysisJob, config *models.BatchAnalysisConfig) error {
	// 更新任務狀態為運行中
	now := time.Now()
	job.Status = "running"
	job.StartedAt = &now
	if err := bas.db.Save(job).Error; err != nil {
		return fmt.Errorf("更新任務狀態失敗: %v", err)
	}

	defer func() {
		// 確保任務狀態被更新
		completedAt := time.Now()
		job.CompletedAt = &completedAt
		if job.Status == "running" {
			job.Status = "completed"
		}
		bas.db.Save(job)
	}()

	// 獲取要分析的期數列表
	analysisResults, err := bas.getAnalysisPeriods(config.LotteryType, job.ReferenceDate, int(config.BatchAnalysisRange))
	if err != nil {
		job.Status = "failed"
		errorMsg := err.Error()
		job.ErrorMessage = &errorMsg
		return err
	}

	if len(analysisResults) == 0 {
		job.Status = "completed"
		return nil
	}

	// 更新任務的期數信息
	job.AnalysisStartPeriod = analysisResults[len(analysisResults)-1].Period
	job.AnalysisEndPeriod = analysisResults[0].Period
	job.TotalPeriods = uint16(len(analysisResults))

	// 對每一期進行分析
	for i, result := range analysisResults {
		if err := bas.analyzeForPeriod(job, config, result, i); err != nil {
			log.Printf("期數 %d 分析失敗: %v", result.Period, err)
			continue
		}

		// 更新進度
		job.ProcessedPeriods = uint16(i + 1)
		bas.db.Save(job)
	}

	return nil
}

// analyzeForPeriod 為特定期數執行分析
func (bas *BatchAnalysisService) analyzeForPeriod(job *models.BatchAnalysisJob, config *models.BatchAnalysisConfig, currentResult models.LottoResult, index int) error {
	// 獲取分析用的歷史數據
	historyResults, err := bas.getHistoryData(config.LotteryType, currentResult.DrawDate, int(config.PeriodNum))
	if err != nil {
		return fmt.Errorf("獲取歷史數據失敗: %v", err)
	}

	if len(historyResults) < int(config.PeriodNum) {
		return fmt.Errorf("歷史數據不足，需要 %d 期，實際 %d 期", config.PeriodNum, len(historyResults))
	}

	// 獲取預測期的實際開獎結果
	predictResults, err := bas.getPredictData(config.LotteryType, currentResult.DrawDate, int(config.AheadNum))
	if err != nil {
		log.Printf("獲取預測期數據失敗: %v", err)
		// 預測期數據可能還沒有，繼續分析
	}

	// 轉換為分析器需要的格式
	drawResults := bas.convertToDrawResults(historyResults)

	// 創建分析器配置
	analysisConfig := AnalysisConfig{
		FirstGroupSize:  int(config.Comb1),
		SecondGroupSize: int(config.Comb2),
		TargetGroupSize: int(config.Comb3),
		MaxRange:        int(config.MaxRange),
		LookAheadCount:  int(config.AheadNum),
	}

	// 執行分析
	analyzer := NewLottoAnalyzer(analysisConfig, drawResults)
	statResults, occurrences, err := analyzer.AnalyzeWithProgress()
	if err != nil {
		return fmt.Errorf("分析執行失敗: %v", err)
	}

	// 篩選結果
	filteredResults := bas.filterResults(statResults, config)

	// 保存分析結果
	if err := bas.saveAnalysisResults(job, config, currentResult, filteredResults, predictResults); err != nil {
		return fmt.Errorf("保存分析結果失敗: %v", err)
	}

	// 保存預測號碼和命中記錄
	if err := bas.savePredictionData(job, config, currentResult, filteredResults, predictResults); err != nil {
		return fmt.Errorf("保存預測數據失敗: %v", err)
	}

	// 記錄 occurrences 統計（可選）
	_ = occurrences

	return nil
}

// getLatestDrawDate 獲取最新開獎日期
func (bas *BatchAnalysisService) getLatestDrawDate(lotteryType string) (time.Time, error) {
	var result models.LottoResult
	var tableName string

	switch lotteryType {
	case "daily539":
		tableName = "lotto539_results"
	case "super_lotto638":
		tableName = "super_lotto_results"
	case "lotto649":
		tableName = "lotto649_results"
	case "lotto_hk":
		tableName = "lotto_hk_results"
	default:
		return time.Time{}, fmt.Errorf("不支持的彩種類型: %s", lotteryType)
	}

	if err := bas.db.Table(tableName).Order("draw_date DESC").First(&result).Error; err != nil {
		return time.Time{}, err
	}

	drawDate, err := time.Parse("2006-01-02", result.DrawDate)
	if err != nil {
		return time.Time{}, fmt.Errorf("解析開獎日期失敗: %v", err)
	}

	return drawDate, nil
}

// getAnalysisPeriods 獲取要分析的期數列表
func (bas *BatchAnalysisService) getAnalysisPeriods(lotteryType string, referenceDate time.Time, count int) ([]models.LottoResult, error) {
	var results []models.LottoResult
	var tableName string

	switch lotteryType {
	case "daily539":
		tableName = "lotto539_results"
	case "super_lotto638":
		tableName = "super_lotto_results"
	case "lotto649":
		tableName = "lotto649_results"
	case "lotto_hk":
		tableName = "lotto_hk_results"
	default:
		return nil, fmt.Errorf("不支持的彩種類型: %s", lotteryType)
	}

	if err := bas.db.Table(tableName).
		Where("draw_date <= ?", referenceDate.Format("2006-01-02")).
		Order("draw_date DESC").
		Limit(count).
		Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

// getHistoryData 獲取歷史數據
func (bas *BatchAnalysisService) getHistoryData(lotteryType string, endDate string, count int) ([]models.LottoResult, error) {
	var results []models.LottoResult
	var tableName string

	switch lotteryType {
	case "daily539":
		tableName = "lotto539_results"
	case "super_lotto638":
		tableName = "super_lotto_results"
	case "lotto649":
		tableName = "lotto649_results"
	case "lotto_hk":
		tableName = "lotto_hk_results"
	default:
		return nil, fmt.Errorf("不支持的彩種類型: %s", lotteryType)
	}

	if err := bas.db.Table(tableName).
		Where("draw_date <= ?", endDate).
		Order("draw_date DESC").
		Limit(count).
		Find(&results).Error; err != nil {
		return nil, err
	}

	// 反轉順序，使最舊的在前面
	for i, j := 0, len(results)-1; i < j; i, j = i+1, j-1 {
		results[i], results[j] = results[j], results[i]
	}

	return results, nil
}

// getPredictData 獲取預測期數據
func (bas *BatchAnalysisService) getPredictData(lotteryType string, startDate string, count int) ([]models.LottoResult, error) {
	var results []models.LottoResult
	var tableName string

	switch lotteryType {
	case "daily539":
		tableName = "lotto539_results"
	case "super_lotto638":
		tableName = "super_lotto_results"
	case "lotto649":
		tableName = "lotto649_results"
	case "lotto_hk":
		tableName = "lotto_hk_results"
	default:
		return nil, fmt.Errorf("不支持的彩種類型: %s", lotteryType)
	}

	if err := bas.db.Table(tableName).
		Where("draw_date > ?", startDate).
		Order("draw_date ASC").
		Limit(count).
		Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

// convertToDrawResults 轉換為分析器需要的格式
func (bas *BatchAnalysisService) convertToDrawResults(results []models.LottoResult) []DrawResult {
	drawResults := make([]DrawResult, len(results))

	for i, result := range results {
		numbers := make([]int, len(result.DrawNumberSize))
		copy(numbers, result.DrawNumberSize)

		// 對於非威力彩的彩種，加入特別號
		if result.SpecialNumber > 0 {
			numbers = append(numbers, result.SpecialNumber)
		}

		drawResults[i] = DrawResult{
			Numbers: numbers,
			Period:  strconv.Itoa(result.Period),
		}
	}

	return drawResults
}

// filterResults 篩選結果
func (bas *BatchAnalysisService) filterResults(results []StatResult, config *models.BatchAnalysisConfig) []StatResult {
	var filtered []StatResult

	for _, result := range results {
		// 根據配置篩選
		if result.ConsecutiveHits >= int(config.ConsecutiveHits) &&
			result.TargetProbability >= config.TargetProbability {
			filtered = append(filtered, result)
		}
	}

	return filtered
}

// saveAnalysisResults 保存分析結果
func (bas *BatchAnalysisService) saveAnalysisResults(job *models.BatchAnalysisJob, config *models.BatchAnalysisConfig, currentResult models.LottoResult, results []StatResult, predictResults []models.LottoResult) error {
	analysisDate, err := time.Parse("2006-01-02", currentResult.DrawDate)
	if err != nil {
		return fmt.Errorf("解析分析日期失敗: %v", err)
	}

	for _, result := range results {
		analysisResult := models.BatchAnalysisResult{
			JobID:             job.ID,
			ConfigID:          config.ID,
			LotteryType:       config.LotteryType,
			AnalysisPeriod:    currentResult.Period,
			AnalysisDate:      analysisDate,
			FirstNumbers:      models.NumberArray(result.FirstNumbers),
			SecondNumbers:     models.NumberArray(result.SecondNumbers),
			TargetNumbers:     models.NumberArray(result.TargetNumbers),
			Gap:               uint8(result.Gap),
			TargetGap:         uint8(result.TargetGap),
			TargetMatches:     uint16(result.TargetMatches),
			TargetProbability: result.TargetProbability,
			ConsecutiveHits:   uint8(result.ConsecutiveHits),
			RankScore:         float64(result.Rank),
		}

		// 如果有預測結果，設置預測期數和日期
		if len(predictResults) > 0 {
			predictPeriod := predictResults[0].Period
			analysisResult.PredictPeriod = &predictPeriod

			predictDate, err := time.Parse("2006-01-02", predictResults[0].DrawDate)
			if err == nil {
				analysisResult.PredictDate = &predictDate
			}
		}

		if err := bas.db.Create(&analysisResult).Error; err != nil {
			return fmt.Errorf("保存分析結果失敗: %v", err)
		}
	}

	return nil
}

// savePredictionData 保存預測數據
func (bas *BatchAnalysisService) savePredictionData(job *models.BatchAnalysisJob, config *models.BatchAnalysisConfig, currentResult models.LottoResult, results []StatResult, predictResults []models.LottoResult) error {
	analysisDate, err := time.Parse("2006-01-02", currentResult.DrawDate)
	if err != nil {
		return fmt.Errorf("解析分析日期失敗: %v", err)
	}

	// 提取預測號碼
	predictedNumbers := bas.extractPredictedNumbers(results)

	// 保存預測號碼記錄
	for predType, numbers := range predictedNumbers {
		for i, numberList := range numbers {
			predictionNumber := models.BatchPredictionNumber{
				JobID:            job.ID,
				ConfigID:         config.ID,
				LotteryType:      config.LotteryType,
				AnalysisPeriod:   currentResult.Period,
				AnalysisDate:     analysisDate,
				PredictionType:   predType,
				PredictedNumbers: models.NumberArray(numberList),
				SortOrder:        uint16(i),
			}

			// 如果有預測結果，設置預測期數和日期
			if len(predictResults) > 0 {
				predictPeriod := predictResults[0].Period
				predictionNumber.PredictPeriod = &predictPeriod

				predictDate, err := time.Parse("2006-01-02", predictResults[0].DrawDate)
				if err == nil {
					predictionNumber.PredictDate = &predictDate
				}
			}

			if err := bas.db.Create(&predictionNumber).Error; err != nil {
				return fmt.Errorf("保存預測號碼失敗: %v", err)
			}

			// 如果有實際開獎結果，計算命中情況
			if len(predictResults) > 0 {
				if err := bas.savePredictionHit(job, config, &predictionNumber, predictResults[0]); err != nil {
					log.Printf("保存命中記錄失敗: %v", err)
				}
			}
		}
	}

	return nil
}

// extractPredictedNumbers 提取預測號碼
func (bas *BatchAnalysisService) extractPredictedNumbers(results []StatResult) map[string][][]int {
	predicted := make(map[string][][]int)

	// 預測號碼（取前10個最佳結果的目標號碼）
	var predictNumbers [][]int
	for i, result := range results {
		if i >= 10 {
			break
		}
		if len(result.TargetNumbers) > 0 {
			predictNumbers = append(predictNumbers, result.TargetNumbers)
		}
	}
	predicted["predict_numbers"] = predictNumbers

	// 未出現號碼-預測次數（這裡簡化處理，實際應該根據具體邏輯計算）
	var nonAppearedFreq [][]int
	// TODO: 實現未出現號碼的統計邏輯
	predicted["non_appeared_frequency"] = nonAppearedFreq

	// 未出現號碼-大小排序
	var nonAppearedSize [][]int
	// TODO: 實現未出現號碼的大小排序邏輯
	predicted["non_appeared_size"] = nonAppearedSize

	// 尾數統計
	var tailNumbers [][]int
	// TODO: 實現尾數統計邏輯
	predicted["tail_numbers"] = tailNumbers

	return predicted
}

// savePredictionHit 保存預測命中記錄
func (bas *BatchAnalysisService) savePredictionHit(job *models.BatchAnalysisJob, config *models.BatchAnalysisConfig, prediction *models.BatchPredictionNumber, actualResult models.LottoResult) error {
	// 計算命中情況
	predictedNums := []int(prediction.PredictedNumbers)
	actualNums := []int(actualResult.DrawNumberSize)

	// 如果有特別號，加入實際號碼
	if actualResult.SpecialNumber > 0 {
		actualNums = append(actualNums, actualResult.SpecialNumber)
	}

	hitNumbers := bas.calculateHitNumbers(predictedNums, actualNums)
	hitCount := len(hitNumbers)
	totalPredicted := len(predictedNums)
	hitRate := 0.0
	if totalPredicted > 0 {
		hitRate = float64(hitCount) / float64(totalPredicted)
	}

	// 檢查是否命中特別號
	isSpecialHit := false
	if actualResult.SpecialNumber > 0 {
		for _, num := range predictedNums {
			if num == actualResult.SpecialNumber {
				isSpecialHit = true
				break
			}
		}
	}

	hit := models.BatchPredictionHit{
		JobID:            job.ID,
		ConfigID:         config.ID,
		PredictionID:     prediction.ID,
		LotteryType:      config.LotteryType,
		AnalysisPeriod:   prediction.AnalysisPeriod,
		PredictPeriod:    actualResult.Period,
		PredictedNumbers: models.NumberArray(predictedNums),
		ActualNumbers:    models.NumberArray(actualNums),
		HitNumbers:       models.NumberArray(hitNumbers),
		HitCount:         uint8(hitCount),
		TotalPredicted:   uint8(totalPredicted),
		HitRate:          hitRate,
		IsSpecialHit:     isSpecialHit,
	}

	return bas.db.Create(&hit).Error
}

// calculateHitNumbers 計算命中號碼
func (bas *BatchAnalysisService) calculateHitNumbers(predicted, actual []int) []int {
	actualSet := make(map[int]bool)
	for _, num := range actual {
		actualSet[num] = true
	}

	var hits []int
	for _, num := range predicted {
		if actualSet[num] {
			hits = append(hits, num)
		}
	}

	sort.Ints(hits)
	return hits
}
