package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"lottery/database"
	. "lottery/models"
	. "lottery/utils"
)

// JWT 中間件
func TokenAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 從授權標頭獲取 Token
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, ErrorMsg{
				Error: "Authorization header is missing",
			})
			c.Abort()
			return
		}

		// 驗證 Bearer Token 格式
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, ErrorMsg{
				Error: "Invalid authorization format",
			})
			c.Abort()
			return
		}

		db := database.ConnectDB()
		defer database.CloseDB(db)

		s := NewTokenService(db)

		tokenString := parts[1]

		// 解析 Token
		claims, _, err := s.<PERSON>(tokenString)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, ErrorMsg{
				Error: "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// 將 Claims 附加到上下文中
		c.Set("claims", claims)
		c.Set("user_id", claims.UserID)
		c.Set("is_admin", claims.IsAdmin)

		c.Next()
	}
}

func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, ErrorMsg{
				Msg: "Authorization header is missing",
			})
			c.Abort()
			return
		}

		// 驗證 Bearer Token 格式
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization format",
			})
			c.Abort()
			return
		}

		tokenString := parts[1]

		db := database.ConnectDB()
		defer database.CloseDB(db)

		s := NewTokenService(db)

		_, _, err := s.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, ErrorMsg{
				Msg:   "Invalid or expired token",
				Error: err.Error(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

func AdminOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get("claims")
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorMsg{
				Msg: "無效的 Token",
			})
			c.Abort()
			return
		}

		tokenClaims, ok := claims.(*TokenClaims)
		if !ok || !tokenClaims.IsAdmin {
			c.JSON(http.StatusForbidden, ErrorMsg{
				Msg: "權限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
