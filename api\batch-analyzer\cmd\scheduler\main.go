package main

import (
	"log"
	batch_analyzer "lottery/batch-analyzer"
)

func main() {
	log.Println("啟動批次分析調度器服務...")
	log.Println("功能說明:")
	log.Println("- 每晚 22:30 自動執行批次分析")
	log.Println("- 啟動時會檢查並執行一次分析（如果需要）")
	log.Println("- 自動避免重複計算")

	// 創建調度器
	scheduler := batch_analyzer.NewScheduler()

	// 啟動調度器（這會阻塞直到收到停止信號）
	if err := scheduler.Start(); err != nil {
		log.Fatalf("調度器啟動失敗: %v", err)
	}

	log.Println("批次分析調度器已正常關閉")
}
