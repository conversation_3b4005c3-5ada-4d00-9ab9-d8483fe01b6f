package main

import (
	"log"
	batch_analyzer "lottery/batch-analyzer"
	"time"
)

func main() {
	log.Println("測試調度器啟動分析功能...")

	// 創建調度器
	scheduler := batch_analyzer.NewScheduler()

	// 測試重複檢查功能
	log.Println("=== 測試重複檢查功能 ===")

	// 注意：由於方法是私有的，我們無法直接測試
	// 但可以通過觀察日誌來驗證功能
	log.Println("注意：重複檢查功能將在實際執行時通過日誌顯示")

	// 測試手動觸發（不強制）
	log.Println("=== 測試手動觸發分析（檢查重複）===")
	if err := scheduler.TriggerManualAnalysis(); err != nil {
		log.Printf("手動觸發失敗: %v", err)
	} else {
		log.Println("手動觸發成功")
	}

	// 等待一下
	time.Sleep(2 * time.Second)

	// 測試強制執行
	log.Println("=== 測試強制執行分析 ===")
	if err := scheduler.TriggerManualAnalysisWithForce(true); err != nil {
		log.Printf("強制執行失敗: %v", err)
	} else {
		log.Println("強制執行成功")
	}

	log.Println("測試完成")
}
