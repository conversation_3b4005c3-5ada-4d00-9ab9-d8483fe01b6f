-- 批次分析參數配置表
CREATE TABLE IF NOT EXISTS `batch_analysis_configs` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL COMMENT '配置名稱',
    `lottery_type` VARCHAR(20) NOT NULL COMMENT '彩種類型 (daily539, super_lotto638, lotto649, lotto_hk)',
    `analysis_method` VARCHAR(20) NOT NULL DEFAULT 'ball-follow' COMMENT '分析方法 (ball-follow, tail, pattern)',
    `comb1` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '第一組組合數',
    `comb2` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '第二組組合數', 
    `comb3` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '第三組組合數',
    `period_num` SMALLINT UNSIGNED NOT NULL DEFAULT 50 COMMENT '推算期數',
    `max_range` TINYINT UNSIGNED NOT NULL DEFAULT 20 COMMENT '拖牌區間',
    `ahead_num` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '預測期數',
    `batch_analysis_range` SMALLINT UNSIGNED NOT NULL DEFAULT 50 COMMENT '分析期數',
    `consecutive_hits` TINYINT UNSIGNED NOT NULL DEFAULT 3 COMMENT '連續拖出次數',
    `target_probability` DECIMAL(5,4) NOT NULL DEFAULT 1.0000 COMMENT '準確率',
    `statistic_type` VARCHAR(20) NOT NULL DEFAULT 'group' COMMENT '統計方式',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否啟用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_lottery_type` (`lottery_type`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次分析參數配置表';

-- 批次分析任務記錄表
CREATE TABLE IF NOT EXISTS `batch_analysis_jobs` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `config_id` BIGINT UNSIGNED NOT NULL COMMENT '配置ID',
    `lottery_type` VARCHAR(20) NOT NULL COMMENT '彩種類型',
    `reference_date` DATE NOT NULL COMMENT '參考日期',
    `analysis_start_period` INT NOT NULL COMMENT '分析開始期數',
    `analysis_end_period` INT NOT NULL COMMENT '分析結束期數',
    `total_periods` SMALLINT UNSIGNED NOT NULL COMMENT '總分析期數',
    `processed_periods` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已處理期數',
    `status` ENUM('pending', 'running', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '任務狀態',
    `started_at` DATETIME NULL COMMENT '開始時間',
    `completed_at` DATETIME NULL COMMENT '完成時間',
    `error_message` TEXT NULL COMMENT '錯誤訊息',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`config_id`) REFERENCES `batch_analysis_configs`(`id`) ON DELETE CASCADE,
    INDEX `idx_config_id` (`config_id`),
    INDEX `idx_lottery_type` (`lottery_type`),
    INDEX `idx_reference_date` (`reference_date`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次分析任務記錄表';

-- 批次分析結果表
CREATE TABLE IF NOT EXISTS `batch_analysis_results` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `job_id` BIGINT UNSIGNED NOT NULL COMMENT '任務ID',
    `config_id` BIGINT UNSIGNED NOT NULL COMMENT '配置ID',
    `lottery_type` VARCHAR(20) NOT NULL COMMENT '彩種類型',
    `analysis_period` INT NOT NULL COMMENT '分析期數',
    `analysis_date` DATE NOT NULL COMMENT '分析日期',
    `predict_period` INT NULL COMMENT '預測期數',
    `predict_date` DATE NULL COMMENT '預測日期',
    `first_numbers` JSON NOT NULL COMMENT '第一組號碼',
    `second_numbers` JSON NOT NULL COMMENT '第二組號碼',
    `target_numbers` JSON NULL COMMENT '目標組號碼',
    `gap` TINYINT UNSIGNED NOT NULL COMMENT '間隔期數',
    `target_gap` TINYINT UNSIGNED NOT NULL COMMENT '目標間隔期數',
    `target_matches` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '目標匹配數',
    `target_probability` DECIMAL(8,6) NOT NULL DEFAULT 0.000000 COMMENT '目標準確率',
    `consecutive_hits` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '連續拖出次數',
    `rank_score` DECIMAL(10,6) NOT NULL DEFAULT 0.000000 COMMENT '排名分數',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`job_id`) REFERENCES `batch_analysis_jobs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`config_id`) REFERENCES `batch_analysis_configs`(`id`) ON DELETE CASCADE,
    INDEX `idx_job_id` (`job_id`),
    INDEX `idx_config_id` (`config_id`),
    INDEX `idx_lottery_type` (`lottery_type`),
    INDEX `idx_analysis_period` (`analysis_period`),
    INDEX `idx_predict_period` (`predict_period`),
    INDEX `idx_target_probability` (`target_probability`),
    INDEX `idx_consecutive_hits` (`consecutive_hits`),
    INDEX `idx_rank_score` (`rank_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次分析結果表';

-- 批次預測號碼記錄表
CREATE TABLE IF NOT EXISTS `batch_prediction_numbers` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `job_id` BIGINT UNSIGNED NOT NULL COMMENT '任務ID',
    `config_id` BIGINT UNSIGNED NOT NULL COMMENT '配置ID',
    `lottery_type` VARCHAR(20) NOT NULL COMMENT '彩種類型',
    `analysis_period` INT NOT NULL COMMENT '分析期數',
    `analysis_date` DATE NOT NULL COMMENT '分析日期',
    `predict_period` INT NULL COMMENT '預測期數',
    `predict_date` DATE NULL COMMENT '預測日期',
    `prediction_type` VARCHAR(20) NOT NULL COMMENT '預測類型 (predict_numbers, non_appeared_frequency, non_appeared_size, tail_numbers)',
    `predicted_numbers` JSON NOT NULL COMMENT '預測號碼列表',
    `frequency_count` SMALLINT UNSIGNED NULL COMMENT '預測次數(僅用於未出現號碼)',
    `sort_order` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序順序',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`job_id`) REFERENCES `batch_analysis_jobs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`config_id`) REFERENCES `batch_analysis_configs`(`id`) ON DELETE CASCADE,
    INDEX `idx_job_id` (`job_id`),
    INDEX `idx_config_id` (`config_id`),
    INDEX `idx_lottery_type` (`lottery_type`),
    INDEX `idx_analysis_period` (`analysis_period`),
    INDEX `idx_predict_period` (`predict_period`),
    INDEX `idx_prediction_type` (`prediction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次預測號碼記錄表';

-- 批次預測命中記錄表
CREATE TABLE IF NOT EXISTS `batch_prediction_hits` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `job_id` BIGINT UNSIGNED NOT NULL COMMENT '任務ID',
    `config_id` BIGINT UNSIGNED NOT NULL COMMENT '配置ID',
    `prediction_id` BIGINT UNSIGNED NOT NULL COMMENT '預測記錄ID',
    `lottery_type` VARCHAR(20) NOT NULL COMMENT '彩種類型',
    `analysis_period` INT NOT NULL COMMENT '分析期數',
    `predict_period` INT NOT NULL COMMENT '預測期數',
    `predicted_numbers` JSON NOT NULL COMMENT '預測號碼',
    `actual_numbers` JSON NOT NULL COMMENT '實際開獎號碼',
    `hit_numbers` JSON NOT NULL COMMENT '命中號碼',
    `hit_count` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '命中個數',
    `total_predicted` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '總預測個數',
    `hit_rate` DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '命中率',
    `is_special_hit` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否命中特別號',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`job_id`) REFERENCES `batch_analysis_jobs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`config_id`) REFERENCES `batch_analysis_configs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`prediction_id`) REFERENCES `batch_prediction_numbers`(`id`) ON DELETE CASCADE,
    INDEX `idx_job_id` (`job_id`),
    INDEX `idx_config_id` (`config_id`),
    INDEX `idx_prediction_id` (`prediction_id`),
    INDEX `idx_lottery_type` (`lottery_type`),
    INDEX `idx_predict_period` (`predict_period`),
    INDEX `idx_hit_count` (`hit_count`),
    INDEX `idx_hit_rate` (`hit_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次預測命中記錄表';
