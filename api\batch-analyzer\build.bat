@echo off
setlocal enabledelayedexpansion

@REM 顯示開始時間
set start_time=%time%
echo 開始時間：%start_time%

set DEFAULT_OS=linux
set DEFAULT_NAME=batch_analyzer
set TARGET_OS=%DEFAULT_OS%
set OUTPUT_FILE=%DEFAULT_NAME%

:parse_args
if "%1"=="" goto end_parse_args
if "%1"=="-m" (
    set "TARGET_OS=%2"
    shift
)

if "%1"=="-o" (
    set "OUTPUT_FILE=%2"
    shift
)
shift
goto parse_args
:end_parse_args

@REM 將命令提示符的代碼頁更改為 UTF-8，以支持顯示中文字符。
chcp 65001 > nul

echo 正在建置批次分析調度器...
set GOOS=%TARGET_OS%

if "%TARGET_OS%"=="windows" (
    set OUTPUT_FILE="%OUTPUT_FILE%.exe"
    set GOARCH=amd64
    set GOOS=windows
) else if "%TARGET_OS%"=="darwin" (
    set OUTPUT_FILE="%OUTPUT_FILE%_mac"
    set GOARCH=arm64
    @REM 舊版本macos使用amd64
) else (
    set OUTPUT_FILE=%OUTPUT_FILE%
    set GOARCH=amd64
)

echo 目標平台：%GOOS%/%GOARCH%
echo 輸出檔案：%OUTPUT_FILE%

go build -o %OUTPUT_FILE% ./cmd/scheduler/main.go

if %errorlevel% neq 0 (
    echo 建置失敗！
    exit /b %errorlevel%
)

echo 建置成功！
echo 輸出檔案：%OUTPUT_FILE%

@REM 顯示結束時間
set end_time=%time%
echo 結束時間：%end_time%
