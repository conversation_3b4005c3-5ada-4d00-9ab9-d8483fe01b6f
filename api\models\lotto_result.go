package models

// 樂透開獎年月
const (
	SuperLottoMonth = "2008-01"
	Lotto649Month   = "2004-01"
	Daily539Month   = "2007-01"
	LottoHKMonth    = "2002-07"
	CaLottoMonth    = "2022-03"
)

const (
	// 威力彩
	SuperLotto638 LottoType = 1
	// 大樂透
	Lotto649 LottoType = 2
	// 今彩539
	Daily539 LottoType = 3
	// 3星彩, 3D
	ThreeStar LottoType = 4
	// 4星彩, 4D
	FourStar LottoType = 5
	// 39樂合彩
	Lotto39 LottoType = 6
	// 49樂合彩
	Lotto49 LottoType = 7
	// 六合彩
	LottoHK LottoType = 8
	// 加州天天樂
	CaLotto LottoType = 9
)

type LottoType int

const (
	SuperLotto638Str = "super_lotto638" // 威力彩
	Lotto649Str      = "lotto649"       // 大樂透
	Daily539Str      = "daily539"       // 今彩539
	ThreeStarStr     = "three_star"     // 3星彩, 3D
	FourStarStr      = "four_star"      // 4星彩, 4D
	Lotto39Str       = "lotto39"        // 39樂合彩
	Lotto49Str       = "lotto49"        // 49樂合彩
	LottoHKStr       = "lotto_hk"       // 六合彩
	CaLottoStr       = "ca_lotto"       // 加州天天樂
)

type LottoTypeStr string

type LottoFilter struct {
	DrawType  LottoTypeStr `form:"draw_type" json:"draw_type" binding:"required"`
	DrawDate  string       `form:"draw_date" json:"draw_date"`
	DateStart string       `form:"date_start" json:"date_start"`
	DateEnd   string       `form:"date_end" json:"date_end"`
	Limit     int          `form:"limit" json:"limit"`
	Offset    int          `form:"offset" json:"offset"`
	Ascending bool         `form:"ascending" json:"ascending"`
}

type LottoResult struct {
	// ID               uint64      `gorm:"primaryKey" form:"id" json:"id"`
	Period           int         `gorm:"primaryKey" form:"period" json:"period"`
	DrawDate         string      `form:"draw_date" json:"draw_date"`
	DrawNumberSize   NumberArray `form:"draw_number_size" json:"draw_number_size"`
	DrawNumberAppear NumberArray `form:"draw_number_appear" json:"draw_number_appear"`
	SpecialNumber    int         `form:"special_number" json:"special_number"`
}

type SuperLottoResult struct {
	LottoResult `gorm:"embedded"`
}

type Lotto649Result struct {
	LottoResult `gorm:"embedded"`
}

type Lotto539Result struct {
	LottoResult   `gorm:"embedded"`
	SpecialNumber int `gorm:"-" form:"-" json:"-"`
}

type LottoHKResult struct {
	LottoResult `gorm:"embedded"`
}

type LottoCaliforniaResult struct {
	LottoResult   `gorm:"embedded"`
	SpecialNumber int `gorm:"-" form:"-" json:"-"`
}
