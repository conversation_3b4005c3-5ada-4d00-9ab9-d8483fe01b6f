package models

import (
	"time"
)

// BatchAnalysisConfig 批次分析參數配置
type BatchAnalysisConfig struct {
	ID                 uint64    `gorm:"primaryKey" json:"id"`
	Name               string    `gorm:"size:100;not null" json:"name"`
	LotteryType        string    `gorm:"size:20;not null" json:"lottery_type"`
	AnalysisMethod     string    `gorm:"size:20;not null;default:ball-follow" json:"analysis_method"`
	Comb1              uint8     `gorm:"not null;default:1" json:"comb1"`
	Comb2              uint8     `gorm:"not null;default:1" json:"comb2"`
	Comb3              uint8     `gorm:"not null;default:1" json:"comb3"`
	PeriodNum          uint16    `gorm:"not null;default:50" json:"period_num"`
	MaxRange           uint8     `gorm:"not null;default:20" json:"max_range"`
	AheadNum           uint8     `gorm:"not null;default:1" json:"ahead_num"`
	BatchAnalysisRange uint16    `gorm:"not null;default:50" json:"batch_analysis_range"`
	ConsecutiveHits    uint8     `gorm:"not null;default:3" json:"consecutive_hits"`
	TargetProbability  float64   `gorm:"type:decimal(5,4);not null;default:1.0000" json:"target_probability"`
	StatisticType      string    `gorm:"size:20;not null;default:group" json:"statistic_type"`
	IsActive           bool      `gorm:"not null;default:true" json:"is_active"`
	CreatedAt          time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// BatchAnalysisJob 批次分析任務記錄
type BatchAnalysisJob struct {
	ID                  uint64     `gorm:"primaryKey" json:"id"`
	ConfigID            uint64     `gorm:"not null" json:"config_id"`
	LotteryType         string     `gorm:"size:20;not null" json:"lottery_type"`
	ReferenceDate       time.Time  `gorm:"type:date;not null" json:"reference_date"`
	AnalysisStartPeriod int        `gorm:"not null" json:"analysis_start_period"`
	AnalysisEndPeriod   int        `gorm:"not null" json:"analysis_end_period"`
	TotalPeriods        uint16     `gorm:"not null" json:"total_periods"`
	ProcessedPeriods    uint16     `gorm:"not null;default:0" json:"processed_periods"`
	Status              string     `gorm:"type:enum('pending','running','completed','failed','cancelled');not null;default:pending" json:"status"`
	StartedAt           *time.Time `json:"started_at"`
	CompletedAt         *time.Time `json:"completed_at"`
	ErrorMessage        *string    `gorm:"type:text" json:"error_message"`
	CreatedAt           time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt           time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 關聯
	Config *BatchAnalysisConfig `gorm:"foreignKey:ConfigID" json:"config,omitempty"`
}

// BatchAnalysisResult 批次分析結果
type BatchAnalysisResult struct {
	ID                uint64      `gorm:"primaryKey" json:"id"`
	JobID             uint64      `gorm:"not null" json:"job_id"`
	ConfigID          uint64      `gorm:"not null" json:"config_id"`
	LotteryType       string      `gorm:"size:20;not null" json:"lottery_type"`
	AnalysisPeriod    int         `gorm:"not null" json:"analysis_period"`
	AnalysisDate      time.Time   `gorm:"type:date;not null" json:"analysis_date"`
	PredictPeriod     *int        `json:"predict_period"`
	PredictDate       *time.Time  `gorm:"type:date" json:"predict_date"`
	FirstNumbers      NumberArray `gorm:"type:json;not null" json:"first_numbers"`
	SecondNumbers     NumberArray `gorm:"type:json;not null" json:"second_numbers"`
	TargetNumbers     NumberArray `gorm:"type:json" json:"target_numbers"`
	Gap               uint8       `gorm:"not null" json:"gap"`
	TargetGap         uint8       `gorm:"not null" json:"target_gap"`
	TargetMatches     uint16      `gorm:"not null;default:0" json:"target_matches"`
	TargetProbability float64     `gorm:"type:decimal(8,6);not null;default:0.000000" json:"target_probability"`
	ConsecutiveHits   uint8       `gorm:"not null;default:0" json:"consecutive_hits"`
	RankScore         float64     `gorm:"type:decimal(10,6);not null;default:0.000000" json:"rank_score"`
	CreatedAt         time.Time   `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`

	// 關聯
	Job    *BatchAnalysisJob    `gorm:"foreignKey:JobID" json:"job,omitempty"`
	Config *BatchAnalysisConfig `gorm:"foreignKey:ConfigID" json:"config,omitempty"`
}

// BatchPredictionNumber 批次預測號碼記錄
type BatchPredictionNumber struct {
	ID               uint64      `gorm:"primaryKey" json:"id"`
	JobID            uint64      `gorm:"not null" json:"job_id"`
	ConfigID         uint64      `gorm:"not null" json:"config_id"`
	LotteryType      string      `gorm:"size:20;not null" json:"lottery_type"`
	AnalysisPeriod   int         `gorm:"not null" json:"analysis_period"`
	AnalysisDate     time.Time   `gorm:"type:date;not null" json:"analysis_date"`
	PredictPeriod    *int        `json:"predict_period"`
	PredictDate      *time.Time  `gorm:"type:date" json:"predict_date"`
	PredictionType   string      `gorm:"size:20;not null" json:"prediction_type"`
	PredictedNumbers NumberArray `gorm:"type:json;not null" json:"predicted_numbers"`
	FrequencyCount   *uint16     `json:"frequency_count"`
	SortOrder        uint16      `gorm:"not null;default:0" json:"sort_order"`
	CreatedAt        time.Time   `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`

	// 關聯
	Job    *BatchAnalysisJob    `gorm:"foreignKey:JobID" json:"job,omitempty"`
	Config *BatchAnalysisConfig `gorm:"foreignKey:ConfigID" json:"config,omitempty"`
}

// BatchPredictionHit 批次預測命中記錄
type BatchPredictionHit struct {
	ID               uint64      `gorm:"primaryKey" json:"id"`
	JobID            uint64      `gorm:"not null" json:"job_id"`
	ConfigID         uint64      `gorm:"not null" json:"config_id"`
	PredictionID     uint64      `gorm:"not null" json:"prediction_id"`
	LotteryType      string      `gorm:"size:20;not null" json:"lottery_type"`
	AnalysisPeriod   int         `gorm:"not null" json:"analysis_period"`
	PredictPeriod    int         `gorm:"not null" json:"predict_period"`
	PredictedNumbers NumberArray `gorm:"type:json;not null" json:"predicted_numbers"`
	ActualNumbers    NumberArray `gorm:"type:json;not null" json:"actual_numbers"`
	HitNumbers       NumberArray `gorm:"type:json;not null" json:"hit_numbers"`
	HitCount         uint8       `gorm:"not null;default:0" json:"hit_count"`
	TotalPredicted   uint8       `gorm:"not null;default:0" json:"total_predicted"`
	HitRate          float64     `gorm:"type:decimal(5,4);not null;default:0.0000" json:"hit_rate"`
	IsSpecialHit     bool        `gorm:"not null;default:false" json:"is_special_hit"`
	CreatedAt        time.Time   `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`

	// 關聯
	Job        *BatchAnalysisJob      `gorm:"foreignKey:JobID" json:"job,omitempty"`
	Config     *BatchAnalysisConfig   `gorm:"foreignKey:ConfigID" json:"config,omitempty"`
	Prediction *BatchPredictionNumber `gorm:"foreignKey:PredictionID" json:"prediction,omitempty"`
}
