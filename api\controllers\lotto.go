package controllers

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"lottery/database"
	. "lottery/models"
	. "lottery/utils"
)

func GetLotto(c *gin.Context) {
	var filter LottoFilter

	if err := c.ShouldBind(&filter); err != nil {
		errorMsg := ErrorMsg{
			Msg:   "資料錯誤",
			Error: err.<PERSON>r(),
		}
		ErrorLog(errorMsg)
		c.JSO<PERSON>(http.StatusBadRequest, errorMsg)
		return
	}

	result, err := getLottoResult(&filter)
	if err != nil {
		errorMsg := ErrorMsg{
			Msg:   "查詢失敗",
			Error: err.Error(),
		}
		ErrorLog(errorMsg)
		c.JSO<PERSON>(http.StatusBadRequest, errorMsg)
		return
	}

	c.JSON(http.StatusOK, result)
}

func getLottoResult(filter *LottoFilter) ([]LottoResult, error) {
	var (
		table  interface{}
		result []LottoResult
	)

	conn := database.ConnectDB()
	defer database.CloseDB(conn)

	switch filter.DrawType {
	case SuperLotto638Str:
		table = SuperLottoResult{}
	case Lotto649Str:
		table = Lotto649Result{}
	case Daily539Str:
		table = Lotto539Result{}
	case LottoHKStr:
		table = LottoHKResult{}
	case CaLottoStr:
		table = LottoCaliforniaResult{}
	default:
		return nil, fmt.Errorf("無效的彩種: %s", filter.DrawType)
	}

	filter.DrawDate = strings.ReplaceAll(filter.DrawDate, "/", "-")

	opts := []string{"period", "DATE_FORMAT(draw_date, '%Y/%m/%d') AS draw_date", "draw_number_size", "draw_number_appear"}

	if filter.DrawType != Daily539Str && filter.DrawType != CaLottoStr {
		opts = append(opts, "special_number")
	}

	conn = conn.Select(opts)

	if filter.DrawDate != "" {
		conn = conn.Where("DATE_FORMAT(draw_date, '%Y-%m') = ?", filter.DrawDate)
	}

	if filter.DateStart != "" {
		conn = conn.Where("DATE(draw_date) >= ?", filter.DateStart)
	}

	if filter.DateEnd != "" {
		conn = conn.Where("DATE(draw_date) <= ?", filter.DateEnd)
	}

	// 設置排序
	if filter.Ascending {
		conn = conn.Order("draw_date ASC, period ASC")
	} else {
		conn = conn.Order("draw_date DESC, period DESC")
	}

	// 設置分頁
	if filter.Offset > 0 {
		conn = conn.Offset(filter.Offset)
	}

	if filter.Limit > 0 {
		conn = conn.Limit(filter.Limit)
	}

	if err := conn.Model(&table).Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

// getAllLatestLottoResults 獲取所有彩種的最新開獎結果
func getAllLatestLottoResults() (map[string]LottoResult, error) {
	conn := database.ConnectDB()
	defer database.CloseDB(conn)

	results := make(map[string]LottoResult)

	// 定義所有彩種
	lottoTypes := []struct {
		drawType LottoTypeStr
		table    interface{}
	}{
		{SuperLotto638Str, SuperLottoResult{}},
		{Lotto649Str, Lotto649Result{}},
		{Daily539Str, Lotto539Result{}},
		{LottoHKStr, LottoHKResult{}},
		{CaLottoStr, LottoCaliforniaResult{}},
	}

	for _, lotto := range lottoTypes {
		var result LottoResult

		opts := []string{"period", "DATE_FORMAT(draw_date, '%Y/%m/%d') AS draw_date", "draw_number_size", "draw_number_appear"}

		if lotto.drawType != Daily539Str && lotto.drawType != CaLottoStr {
			opts = append(opts, "special_number")
		}

		query := conn.Model(lotto.table).
			Select(opts).
			Order("draw_date DESC, period DESC").
			Limit(1)

		if err := query.Scan(&result).Error; err != nil {
			ErrorLog(ErrorMsg{
				Msg:   fmt.Sprintf("查詢 %s 最新開獎結果失敗", lotto.drawType),
				Error: err.Error(),
			})
			continue // 繼續處理其他彩種，不要因為一個失敗就全部失敗
		}

		results[string(lotto.drawType)] = result
	}

	return results, nil
}

func GetLottoPredict(c *gin.Context) {
	req := struct {
		DrawType   LottoTypeStr `form:"draw_type" json:"draw_type" binding:"required"`
		DrawDate   string       `form:"draw_date" json:"draw_date" binding:"required"`
		AheadCount int          `form:"ahead_count" json:"ahead_count" binding:"required"`
	}{}

	if err := c.ShouldBind(&req); err != nil {
		errorMsg := ErrorMsg{
			Msg:   "資料錯誤",
			Error: err.Error(),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	predict, err := getLottoPredict(req.DrawType, req.DrawDate, req.AheadCount)
	if err != nil {
		errorMsg := ErrorMsg{
			Msg:   "查詢失敗",
			Error: err.Error(),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	c.JSON(http.StatusOK, predict)
}

// GetLatestLottoResults 獲取所有彩種的最新開獎結果
func GetLatestLottoResults(c *gin.Context) {
	results, err := getAllLatestLottoResults()
	if err != nil {
		errorMsg := ErrorMsg{
			Msg:   "查詢失敗",
			Error: err.Error(),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	c.JSON(http.StatusOK, results)
}

func getLottoPredict(drawType LottoTypeStr, drawDate string, aheadCount int) (LottoResult, error) {
	var table interface{}
	result := LottoResult{}

	db := database.ConnectDB()
	defer database.CloseDB(db)

	switch drawType {
	case SuperLotto638Str:
		table = SuperLottoResult{}
	case Lotto649Str:
		table = Lotto649Result{}
	case Daily539Str:
		table = Lotto539Result{}
	case LottoHKStr:
		table = LottoHKResult{}
	case CaLottoStr:
		table = LottoCaliforniaResult{}
	default:
		return result, fmt.Errorf("無效的彩種: %s", drawType)
	}

	drawDate = strings.ReplaceAll(drawDate, "/", "-")

	opts := []string{"period", "DATE_FORMAT(draw_date, '%Y/%m/%d') AS draw_date", "draw_number_size", "draw_number_appear"}
	if drawType != Daily539Str && drawType != CaLottoStr {
		opts = append(opts, "special_number")
	}

	tx := db.Select(opts)

	err := tx.Model(&table).Where("draw_date > ?", drawDate).Offset(aheadCount - 1).Limit(1).Find(&result).Error

	return result, err
}
