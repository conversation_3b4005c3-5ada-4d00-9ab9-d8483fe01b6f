package batch_analyzer

import (
	"fmt"
	"sort"
	"sync"
)

// AnalysisConfig 分析配置
type AnalysisConfig struct {
	FirstGroupSize  int `json:"firstGroupSize"`
	SecondGroupSize int `json:"secondGroupSize"`
	TargetGroupSize int `json:"targetGroupSize"`
	MaxRange        int `json:"maxRange"`
	LookAheadCount  int `json:"lookAheadCount"`
}

// DrawResult 開獎結果
type DrawResult struct {
	Numbers []int  `json:"numbers"`
	Period  string `json:"period"`
}

// StatResult 統計結果
type StatResult struct {
	FirstNumbers      []int   `json:"firstNumbers"`
	SecondNumbers     []int   `json:"secondNumbers"`
	TargetNumbers     []int   `json:"targetNumbers"`
	Gap               int     `json:"gap"`
	TargetGap         int     `json:"targetGap"`
	TargetMatches     int     `json:"targetMatches"`
	TargetProbability float64 `json:"targetProbability"`
	Rank              int     `json:"rank"`
	ConsecutiveHits   int     `json:"consecutiveHits"`
}

// Occurrence 出現統計
type Occurrence struct {
	Count     int                `json:"count"`
	Periods   []OccurrencePeriod `json:"periods"`
	IsPredict bool               `json:"isPredict"`
}

// OccurrencePeriod 出現期數
type OccurrencePeriod struct {
	FirstPeriod  string `json:"firstPeriod"`
	SecondPeriod string `json:"secondPeriod"`
	TargetPeriod string `json:"targetPeriod"`
}

// CombinationKey 組合鍵
type CombinationKey struct {
	FirstNumbers  []int
	SecondNumbers []int
	Gap           int
	TargetGap     int
}

// String 生成字符串鍵
func (ck CombinationKey) String() string {
	// 確保組合內部排序一致
	sortedFirst := make([]int, len(ck.FirstNumbers))
	copy(sortedFirst, ck.FirstNumbers)
	sort.Ints(sortedFirst)

	sortedSecond := make([]int, len(ck.SecondNumbers))
	copy(sortedSecond, ck.SecondNumbers)
	sort.Ints(sortedSecond)

	return fmt.Sprintf("%v-%v-%d-%d", sortedFirst, sortedSecond, ck.Gap, ck.TargetGap)
}

// FullCombinationKey 完整組合鍵
type FullCombinationKey struct {
	FirstNumbers  []int
	SecondNumbers []int
	TargetNumbers []int
	Gap           int
	TargetGap     int
}

// String 生成字符串鍵
func (fck FullCombinationKey) String() string {
	// 確保組合內部排序一致
	sortedFirst := make([]int, len(fck.FirstNumbers))
	copy(sortedFirst, fck.FirstNumbers)
	sort.Ints(sortedFirst)

	sortedSecond := make([]int, len(fck.SecondNumbers))
	copy(sortedSecond, fck.SecondNumbers)
	sort.Ints(sortedSecond)

	sortedTarget := make([]int, len(fck.TargetNumbers))
	copy(sortedTarget, fck.TargetNumbers)
	sort.Ints(sortedTarget)

	return fmt.Sprintf("%v-%v-%v-%d-%d", sortedFirst, sortedSecond, sortedTarget, fck.Gap, fck.TargetGap)
}

// LottoAnalyzer 彩票分析器
type LottoAnalyzer struct {
	config  AnalysisConfig
	results []DrawResult
	mu      sync.RWMutex
}

// NewLottoAnalyzer 創建新的分析器
func NewLottoAnalyzer(config AnalysisConfig, results []DrawResult) *LottoAnalyzer {
	return &LottoAnalyzer{
		config:  config,
		results: results,
	}
}

// AnalyzeWithProgress 執行分析並返回進度
func (la *LottoAnalyzer) AnalyzeWithProgress() ([]StatResult, map[string]Occurrence, error) {
	la.mu.Lock()
	defer la.mu.Unlock()

	statResults := make(map[string]*StatResult)
	occurrenceResults := make(map[string]Occurrence)
	hitDetails := make(map[string]map[string]bool)

	predictIndex := len(la.results) - 1 + la.config.LookAheadCount

	// 先計算所有 Occurrence 統計
	la.calculateOccurrences(occurrenceResults)

	// 處理預測結果
	for i := 0; i < len(la.results)-la.config.LookAheadCount; i++ {
		firstGroups := la.getCombinations(la.results[i].Numbers, la.config.FirstGroupSize)

		for j := i + 1; j < len(la.results) && j-i <= la.config.MaxRange; j++ {
			secondGroups := la.getCombinations(la.results[j].Numbers, la.config.SecondGroupSize)
			gap := j - i

			for k := j + 1; k-j <= la.config.MaxRange; k++ {
				if k > predictIndex {
					break
				}

				targetGap := k - j
				var targetGroups [][]int

				if k < len(la.results) {
					targetGroups = la.getCombinations(la.results[k].Numbers, la.config.TargetGroupSize)
				}

				// 處理組合
				for _, firstGroup := range firstGroups {
					for _, secondGroup := range secondGroups {
						key := CombinationKey{
							FirstNumbers:  firstGroup,
							SecondNumbers: secondGroup,
							Gap:           gap,
							TargetGap:     targetGap,
						}.String()

						if statResults[key] == nil {
							statResults[key] = &StatResult{
								FirstNumbers:  firstGroup,
								SecondNumbers: secondGroup,
								Gap:           gap,
								TargetGap:     targetGap,
							}
						}

						// 如果有目標組合，檢查匹配
						if k < len(la.results) {
							for _, targetGroup := range targetGroups {
								if la.hasIntersection(firstGroup, targetGroup) && la.hasIntersection(secondGroup, targetGroup) {
									statResults[key].TargetMatches++
									statResults[key].TargetNumbers = targetGroup

									// 記錄命中詳情
									fullKey := FullCombinationKey{
										FirstNumbers:  firstGroup,
										SecondNumbers: secondGroup,
										TargetNumbers: targetGroup,
										Gap:           gap,
										TargetGap:     targetGap,
									}.String()

									if hitDetails[fullKey] == nil {
										hitDetails[fullKey] = make(map[string]bool)
									}
									hitDetails[fullKey][la.results[k].Period] = true
								}
							}
						}
					}
				}
			}
		}
	}

	// 計算準確率和連續拖出次數
	var results []StatResult
	for key, stat := range statResults {
		if occurrence, exists := occurrenceResults[key]; exists && occurrence.IsPredict {
			if stat.TargetMatches > 0 && occurrence.Count > 0 {
				stat.TargetProbability = float64(stat.TargetMatches) / float64(occurrence.Count)
				stat.ConsecutiveHits = la.calculateConsecutiveHits(stat, occurrence, hitDetails)
				results = append(results, *stat)
			}
		}
	}

	// 排序和排名
	la.sortAndRankResults(results)

	return results, occurrenceResults, nil
}

// calculateOccurrences 計算出現統計
func (la *LottoAnalyzer) calculateOccurrences(occurrenceResults map[string]Occurrence) {
	predictIndex := len(la.results) - 1 + la.config.LookAheadCount

	for i := 0; i < len(la.results)-la.config.LookAheadCount; i++ {
		firstGroups := la.getCombinations(la.results[i].Numbers, la.config.FirstGroupSize)

		for j := i + 1; j < len(la.results) && j-i <= la.config.MaxRange; j++ {
			secondGroups := la.getCombinations(la.results[j].Numbers, la.config.SecondGroupSize)
			gap := j - i

			for k := j + 1; k-j <= la.config.MaxRange; k++ {
				if k != predictIndex {
					continue
				}
				if k > predictIndex {
					break
				}

				targetGap := k - j

				for _, firstGroup := range firstGroups {
					for _, secondGroup := range secondGroups {
						key := CombinationKey{
							FirstNumbers:  firstGroup,
							SecondNumbers: secondGroup,
							Gap:           gap,
							TargetGap:     targetGap,
						}.String()

						occurrence := Occurrence{
							Count:     0,
							Periods:   []OccurrencePeriod{},
							IsPredict: true,
						}
						occurrenceResults[key] = occurrence
					}
				}
			}
		}
	}

	// 計算實際出現次數
	for i := 0; i < len(la.results)-la.config.LookAheadCount; i++ {
		firstGroups := la.getCombinations(la.results[i].Numbers, la.config.FirstGroupSize)

		for j := i + 1; j < len(la.results) && j-i <= la.config.MaxRange; j++ {
			secondGroups := la.getCombinations(la.results[j].Numbers, la.config.SecondGroupSize)
			gap := j - i

			for k := j + 1; k < len(la.results) && k-j <= la.config.MaxRange; k++ {
				targetGap := k - j

				for _, firstGroup := range firstGroups {
					for _, secondGroup := range secondGroups {
						key := CombinationKey{
							FirstNumbers:  firstGroup,
							SecondNumbers: secondGroup,
							Gap:           gap,
							TargetGap:     targetGap,
						}.String()

						if occurrence, exists := occurrenceResults[key]; exists {
							occurrence.Count++
							occurrence.Periods = append(occurrence.Periods, OccurrencePeriod{
								FirstPeriod:  la.results[i].Period,
								SecondPeriod: la.results[j].Period,
								TargetPeriod: la.results[k].Period,
							})
							occurrenceResults[key] = occurrence
						}
					}
				}
			}
		}
	}
}

// getCombinations 獲取組合
func (la *LottoAnalyzer) getCombinations(numbers []int, size int) [][]int {
	if size <= 0 || size > len(numbers) {
		return [][]int{}
	}

	var result [][]int
	la.generateCombinations(numbers, size, 0, []int{}, &result)
	return result
}

// generateCombinations 生成組合
func (la *LottoAnalyzer) generateCombinations(numbers []int, size int, start int, current []int, result *[][]int) {
	if len(current) == size {
		combination := make([]int, len(current))
		copy(combination, current)
		*result = append(*result, combination)
		return
	}

	for i := start; i < len(numbers); i++ {
		current = append(current, numbers[i])
		la.generateCombinations(numbers, size, i+1, current, result)
		current = current[:len(current)-1]
	}
}

// hasIntersection 檢查兩個數組是否有交集
func (la *LottoAnalyzer) hasIntersection(arr1, arr2 []int) bool {
	set := make(map[int]bool)
	for _, num := range arr1 {
		set[num] = true
	}

	for _, num := range arr2 {
		if set[num] {
			return true
		}
	}
	return false
}

// calculateConsecutiveHits 計算連續拖出次數
func (la *LottoAnalyzer) calculateConsecutiveHits(stat *StatResult, occurrence Occurrence, hitDetails map[string]map[string]bool) int {
	fullKey := FullCombinationKey{
		FirstNumbers:  stat.FirstNumbers,
		SecondNumbers: stat.SecondNumbers,
		TargetNumbers: stat.TargetNumbers,
		Gap:           stat.Gap,
		TargetGap:     stat.TargetGap,
	}.String()

	hitPeriodsMap, exists := hitDetails[fullKey]
	if !exists || len(hitPeriodsMap) == 0 {
		return 0
	}

	// 獲取所有有效期號
	var validPeriods []string
	for _, period := range occurrence.Periods {
		if period.TargetPeriod != "" {
			validPeriods = append(validPeriods, period.TargetPeriod)
		}
	}

	if len(validPeriods) == 0 {
		return 0
	}

	// 按期號排序（假設期號是數字格式）
	sort.Slice(validPeriods, func(i, j int) bool {
		return validPeriods[i] > validPeriods[j] // 降序，最新的在前
	})

	// 從最新期號開始計算連續拖出次數
	consecutiveHits := 0
	for _, period := range validPeriods {
		if hitPeriodsMap[period] {
			consecutiveHits++
		} else {
			break
		}
	}

	return consecutiveHits
}

// sortAndRankResults 排序和排名結果
func (la *LottoAnalyzer) sortAndRankResults(results []StatResult) {
	// 按準確率降序、連續拖出次數降序排序
	sort.Slice(results, func(i, j int) bool {
		if results[i].TargetProbability != results[j].TargetProbability {
			return results[i].TargetProbability > results[j].TargetProbability
		}
		return results[i].ConsecutiveHits > results[j].ConsecutiveHits
	})

	// 設置排名
	for i := range results {
		results[i].Rank = i + 1
	}
}
