package batch_analyzer

import (
	"fmt"
	"log"
	"lottery/database"
	"lottery/models"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

// Scheduler 批次分析調度器
type Scheduler struct {
	cron    *cron.Cron
	service *BatchAnalysisService
	db      *gorm.DB
}

// NewScheduler 創建新的調度器
func NewScheduler() *Scheduler {
	return &Scheduler{
		cron: cron.New(
			cron.WithLocation(time.Local),
			cron.WithChain(
				cron.Recover(cron.DefaultLogger), // 添加恢復機制和日誌記錄
			),
		),
		service: NewBatchAnalysisService(),
		db:      database.ConnectDB(),
	}
}

// Start 啟動調度器
func (s *Scheduler) Start() error {
	log.Println("啟動批次分析調度器...")

	// 初始化默認配置（如果不存在）
	if err := s.initializeDefaultConfigs(); err != nil {
		log.Printf("初始化默認配置失敗: %v", err)
	}

	// 添加每晚 10:30 的定時任務
	_, err := s.cron.AddFunc("30 22 * * *", func() {
		s.executeScheduledAnalysis()
	})
	if err != nil {
		return err
	}

	// 啟動 cron
	s.cron.Start()
	log.Println("批次分析調度器已啟動，將在每晚 22:30 執行分析")

	// 優雅的關閉處理
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)
	<-stop

	log.Println("正在關閉批次分析調度器...")
	s.cron.Stop()
	log.Println("批次分析調度器已關閉")

	return nil
}

// executeScheduledAnalysis 執行定時分析
func (s *Scheduler) executeScheduledAnalysis() {
	log.Println("開始執行定時批次分析...")

	defer func() {
		if r := recover(); r != nil {
			log.Printf("定時分析發生 panic: %v", r)
		}
	}()

	if err := s.service.RunDailyAnalysis(); err != nil {
		log.Printf("定時分析執行失敗: %v", err)
	} else {
		log.Println("定時分析執行完成")
	}
}

// initializeDefaultConfigs 初始化默認配置
func (s *Scheduler) initializeDefaultConfigs() error {
	log.Println("檢查並初始化默認配置...")

	// 檢查是否已有配置
	var count int64
	if err := s.db.Model(&models.BatchAnalysisConfig{}).Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		log.Printf("已存在 %d 個配置，跳過初始化", count)
		return nil
	}

	// 根據 v1.3.5_todo.md 的參數創建默認配置
	defaultConfigs := s.createDefaultConfigs()

	for _, config := range defaultConfigs {
		if err := s.db.Create(&config).Error; err != nil {
			log.Printf("創建默認配置失敗: %v", err)
			continue
		}
		log.Printf("創建默認配置: %s", config.Name)
	}

	return nil
}

// createDefaultConfigs 創建默認配置
func (s *Scheduler) createDefaultConfigs() []models.BatchAnalysisConfig {
	var configs []models.BatchAnalysisConfig

	// 根據 v1.3.5_todo.md 的參數設定
	// 拖牌組合: 全部(1,1,1、1,1,2、1,1,3、1,2,1、1,2,2、1,2,3、1,3,1、1,3,2、1,3,3、2,1,1、2,1,2、2,1,3、2,2,1、2,2,2、2,2,3、2,3,1、2,3,2、2,3,3、3,1,1、3,1,2、3,1,3、3,2,1、3,2,2、3,2,3、3,3,1、3,3,2、3,3,3)
	combos := [][]int{
		{1, 1, 1}, {1, 1, 2}, {1, 1, 3}, {1, 2, 1}, {1, 2, 2}, {1, 2, 3}, {1, 3, 1}, {1, 3, 2}, {1, 3, 3},
		{2, 1, 1}, {2, 1, 2}, {2, 1, 3}, {2, 2, 1}, {2, 2, 2}, {2, 2, 3}, {2, 3, 1}, {2, 3, 2}, {2, 3, 3},
		{3, 1, 1}, {3, 1, 2}, {3, 1, 3}, {3, 2, 1}, {3, 2, 2}, {3, 2, 3}, {3, 3, 1}, {3, 3, 2}, {3, 3, 3},
	}

	// 推算期數: 從30期到300期，間隔30期一次計算，共10個 30/60/90/120/150/180/210/240/270/300
	periodNums := []int{30, 60, 90, 120, 150, 180, 210, 240, 270, 300}

	// 拖牌區間: 從10期到30期，間隔5期一次計算，共5個 10/15/20/25/30
	maxRanges := []int{10, 15, 20, 25, 30}

	configIndex := 1

	// 為今彩539創建所有組合的配置
	for _, combo := range combos {
		for _, periodNum := range periodNums {
			for _, maxRange := range maxRanges {
				config := models.BatchAnalysisConfig{
					Name:               fmt.Sprintf("今彩539_版路分析_%d-%d-%d_期數%d_區間%d", combo[0], combo[1], combo[2], periodNum, maxRange),
					LotteryType:        "daily539",
					AnalysisMethod:     "ball-follow",
					Comb1:              uint8(combo[0]),
					Comb2:              uint8(combo[1]),
					Comb3:              uint8(combo[2]),
					PeriodNum:          uint16(periodNum),
					MaxRange:           uint8(maxRange),
					AheadNum:           1,       // 預測期數: 下1期
					BatchAnalysisRange: 50,      // 分析期數: 50期
					ConsecutiveHits:    3,       // 連續拖出次數: 3次(含)以上
					TargetProbability:  1.0,     // 準確率: 100%(含)以上
					StatisticType:      "group", // 統計方式: 預測組數統計
					IsActive:           true,
				}
				configs = append(configs, config)
				configIndex++

				// 限制配置數量，避免創建過多配置
				if len(configs) >= 100 {
					log.Printf("已創建 %d 個配置，停止創建更多配置", len(configs))
					return configs
				}
			}
		}
	}

	return configs
}

// TriggerManualAnalysis 手動觸發分析
func (s *Scheduler) TriggerManualAnalysis() error {
	log.Println("手動觸發批次分析...")
	return s.service.RunDailyAnalysis()
}

// GetJobStatus 獲取任務狀態
func (s *Scheduler) GetJobStatus(jobID uint64) (*models.BatchAnalysisJob, error) {
	var job models.BatchAnalysisJob
	if err := s.db.Preload("Config").First(&job, jobID).Error; err != nil {
		return nil, err
	}
	return &job, nil
}

// GetActiveConfigs 獲取啟用的配置
func (s *Scheduler) GetActiveConfigs() ([]models.BatchAnalysisConfig, error) {
	var configs []models.BatchAnalysisConfig
	if err := s.db.Where("is_active = ?", true).Find(&configs).Error; err != nil {
		return nil, err
	}
	return configs, nil
}

// GetRecentJobs 獲取最近的任務
func (s *Scheduler) GetRecentJobs(limit int) ([]models.BatchAnalysisJob, error) {
	var jobs []models.BatchAnalysisJob
	if err := s.db.Preload("Config").Order("created_at DESC").Limit(limit).Find(&jobs).Error; err != nil {
		return nil, err
	}
	return jobs, nil
}
