package main

import (
	"fmt"
	"log"
	"lottery/database"
	"lottery/models"
)

func main() {
	log.Println("初始化批次分析配置...")
	
	// 連接資料庫
	db := database.ConnectDB()
	
	// 檢查是否已有配置
	var count int64
	if err := db.Model(&models.BatchAnalysisConfig{}).Count(&count).Error; err != nil {
		log.Fatalf("檢查配置失敗: %v", err)
	}
	
	if count > 0 {
		log.Printf("已存在 %d 個配置，是否要重新初始化？(y/N)", count)
		var input string
		fmt.Scanln(&input)
		if input != "y" && input != "Y" {
			log.Println("取消初始化")
			return
		}
		
		// 刪除現有配置
		if err := db.Where("1 = 1").Delete(&models.BatchAnalysisConfig{}).Error; err != nil {
			log.Fatalf("刪除現有配置失敗: %v", err)
		}
		log.Println("已刪除現有配置")
	}
	
	// 創建默認配置
	configs := createDefaultConfigs()
	
	log.Printf("準備創建 %d 個配置...", len(configs))
	
	for i, config := range configs {
		if err := db.Create(&config).Error; err != nil {
			log.Printf("創建配置 %d 失敗: %v", i+1, err)
			continue
		}
		
		if (i+1)%10 == 0 {
			log.Printf("已創建 %d/%d 個配置", i+1, len(configs))
		}
	}
	
	log.Printf("配置初始化完成，共創建 %d 個配置", len(configs))
}

func createDefaultConfigs() []models.BatchAnalysisConfig {
	var configs []models.BatchAnalysisConfig
	
	// 根據 v1.3.5_todo.md 的參數設定
	combos := [][]int{
		{1, 1, 1}, {1, 1, 2}, {1, 1, 3}, {1, 2, 1}, {1, 2, 2}, {1, 2, 3}, {1, 3, 1}, {1, 3, 2}, {1, 3, 3},
		{2, 1, 1}, {2, 1, 2}, {2, 1, 3}, {2, 2, 1}, {2, 2, 2}, {2, 2, 3}, {2, 3, 1}, {2, 3, 2}, {2, 3, 3},
		{3, 1, 1}, {3, 1, 2}, {3, 1, 3}, {3, 2, 1}, {3, 2, 2}, {3, 2, 3}, {3, 3, 1}, {3, 3, 2}, {3, 3, 3},
	}
	
	periodNums := []int{30, 60, 90, 120, 150, 180, 210, 240, 270, 300}
	maxRanges := []int{10, 15, 20, 25, 30}
	
	configIndex := 1
	
	// 為今彩539創建所有組合的配置
	for _, combo := range combos {
		for _, periodNum := range periodNums {
			for _, maxRange := range maxRanges {
				config := models.BatchAnalysisConfig{
					Name:                fmt.Sprintf("今彩539_版路分析_%d-%d-%d_期數%d_區間%d", combo[0], combo[1], combo[2], periodNum, maxRange),
					LotteryType:         "daily539",
					AnalysisMethod:      "ball-follow",
					Comb1:               uint8(combo[0]),
					Comb2:               uint8(combo[1]),
					Comb3:               uint8(combo[2]),
					PeriodNum:           uint16(periodNum),
					MaxRange:            uint8(maxRange),
					AheadNum:            1,
					BatchAnalysisRange:  50,
					ConsecutiveHits:     3,
					TargetProbability:   1.0,
					StatisticType:       "group",
					IsActive:            true,
				}
				configs = append(configs, config)
				configIndex++
			}
		}
	}
	
	return configs
}
