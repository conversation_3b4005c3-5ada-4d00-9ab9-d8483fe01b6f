package models

import (
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type User struct {
	ID          uint64         `gorm:"primaryKey" form:"id" json:"id"`
	UID         string         `gorm:"<-:create" form:"uid" json:"uid"`
	Pwd         string         `form:"pwd" json:"pwd"`
	IsAdmin     bool           `form:"is_admin" json:"is_admin"`
	Name        string         `form:"name" json:"name"`
	Email       string         `form:"email" json:"email"`
	ExpiresAt   null.Time      `gorm:"<-:update" form:"expires_at" json:"expires_at"` // 僅用於一般使用者
	IsActive    bool           `gorm:"<-:update" form:"is_active" json:"is_active"`
	LastLoginAt null.Time      `gorm:"<-:update" form:"last_login_at" json:"last_login_at"`
	CreatedAt   time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

type UserData struct {
	ID          string      `gorm:"<-:false" json:"id"`
	UID         string      `gorm:"<-:false" json:"uid"`
	Name        *string     `json:"name"`
	Email       *string     `json:"email"`
	ExpiresAt   null.String `json:"expires_at"`
	IsActive    *bool       `json:"is_active"`
	LastLoginAt string      `gorm:"<-:false" json:"last_login_at"`
	CreatedAt   string      `gorm:"<-:false" json:"created_at"`
}

func (u *UserData) TableName() string {
	return "users"
}

type UserDevice struct {
	ID             int64          `gorm:"primaryKey" json:"id"`
	UserID         uint64         `json:"user_id"`
	DeviceID       string         `json:"device_id"`        // 設備唯一標識（包含會話信息）
	StableDeviceID string         `json:"stable_device_id"` // 穩定設備標識（用於識別同一物理設備）
	DeviceName     string         `json:"device_name"`      // 設備名稱(如 iPhone 13)
	UserAgent      string         `json:"user_agent"`       // 用戶代理
	IsActive       bool           `json:"is_active"`        // 當前是否活躍
	LastSeenAt     time.Time      `json:"last_seen_at"`     // 最後活動時間
	Confidence     int            `json:"confidence"`       // 設備識別可信度
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
}

type LoginLog struct {
	ID        uint64    `json:"id"`
	UserID    uint64    `json:"user_id"`
	DeviceID  string    `json:"device_id"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
	IsSuccess bool      `json:"is_success"`
	CreatedAt time.Time `json:"created_at"`
	User      User      `gorm:"foreignKey:UserID"`
}

type RegisterRequest struct {
	UID   string `json:"uid" binding:"required"`
	Pwd   string `json:"pwd" binding:"required"`
	Name  string `json:"name" binding:"required"`
	Email string `json:"email"`
}

type LoginRequest struct {
	UID               string             `json:"uid" binding:"required"`
	Pwd               string             `json:"pwd" binding:"required"`
	DeviceID          string             `json:"device_id,omitempty"`
	DeviceFingerprint *DeviceFingerprint `json:"device_fingerprint,omitempty"`
}

type DeviceFingerprint struct {
	StableDeviceID string `json:"stable_device_id"`
	SessionID      string `json:"session_id"`
	Confidence     int    `json:"confidence"`
	Platform       string `json:"platform"`
	Screen         string `json:"screen"`
}

type LoginResponse struct {
	TokenPair `gorm:"embedded"`
	User      LoginResponseUser `json:"user"`
}

type LoginResponseUser struct {
	ID   uint64 `json:"id"`
	UID  string `json:"uid"`
	Name string `json:"name"`
}

func GetUserByUID(db *gorm.DB, uid string) (User, error) {
	var user User
	if err := db.Where("uid = ?", uid).First(&user); err != nil {
		return user, err.Error
	}

	return user, nil
}

func GetUserByID(db *gorm.DB, id uint64) (User, error) {
	var user User
	if err := db.Where("id = ?", id).First(&user); err != nil {
		return user, err.Error
	}

	return user, nil
}

func IsUserExists(db *gorm.DB, uid string) bool {
	var user User
	db.Select("id").Where("uid = ?", uid).First(&user)
	return user.ID != 0
}

func (u *User) IsValid() bool {
	return u.IsActive &&
		(u.IsAdmin || !u.IsExpired())
}

func (u *User) IsExpired() bool {
	// 比較日期，不比較時間
	return u.ExpiresAt.IsZero() || isDateBefore(u.ExpiresAt.Time, time.Now())
}

func isDateBefore(t1, t2 time.Time) bool {
	return t1.Year() < t2.Year() ||
		(t1.Year() == t2.Year() && t1.YearDay() < t2.YearDay())
}
