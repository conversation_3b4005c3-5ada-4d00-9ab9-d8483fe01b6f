package routes

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	. "lottery/controllers"
	"lottery/middleware"
)

func Routes(db *gorm.DB) *gin.Engine {
	r := gin.Default()

	// CORS 設定
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization", "Upgrade", "Connection", "Sec-WebSocket-Key", "Sec-WebSocket-Version", "Sec-WebSocket-Protocol"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	api := r.Group("/api")
	renderApiRoutes(api)

	return r
}

func renderApiRoutes(api *gin.RouterGroup) {
	// 健康檢查端點（無需認證）
	api.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"time":   time.Now().Format("2006-01-02 15:04:05"),
		})
	})

	auth := api.Group("/auth")
	{
		auth.POST("/register", RegisterHandler)
		auth.POST("/login", LoginHandler)

		auth.POST("/refresh-token", RefreshTokenHandler)

		auth.POST("/logout", LogoutHandler)
	}

	// WebSocket路由（需要單獨處理，不能使用標準的token中間件）
	api.GET("/ws", middleware.WebSocketAuth(), HandleWebSocket)

	// 需要token驗證的路由
	api.Use(middleware.TokenAuth()).
		Use(middleware.AuthRequired())

	// 用戶個人資料相關端點
	api.GET("/profile", GetCurrentUserProfile)
	api.PATCH("/profile", UpdateCurrentUserProfile)
	api.PATCH("/profile/password", ChangePassword)

	api.GET("/lotto", GetLotto)
	api.GET("/lotto/predict", GetLottoPredict)
	api.GET("/lotto/latest", GetLatestLottoResults)

	admin := api.Group("/admin")
	admin.Use(middleware.AdminOnly())

	user := admin.Group("/users")
	{
		user.GET("/", GetUserList)
		user.PATCH("/", UpdateUserByAdmin)
		user.PATCH("/:id/active", UpdateUserActive)
		user.PATCH("/:id/expire", UpdateUserExpire)
		user.DELETE("/:id", DeleteUser)
		user.GET("/:id/devices", GetUserDevices)
		user.GET("/:id/login-history", GetUserLoginHistory)
		user.DELETE("/:id/devices/:device_id", DeleteUserDevice)
	}

	// 設備管理路由
	device := admin.Group("/devices")
	{
		device.GET("/statistics", GetDeviceStatistics)
		device.POST("/cleanup", CleanupDevices)
		device.GET("/duplicates", GetDuplicateDevices)
		device.GET("/user/:user_id", GetUserDevicesDetailed)
		device.DELETE("/:device_id", DeleteDevice)
		device.POST("/merge", MergeDevices)
	}
}
